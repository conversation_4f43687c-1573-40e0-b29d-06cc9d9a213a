<template>
  <div class="input-section">
    <!-- 标题 - 只在没有消息时显示 -->
    <div v-if="!props.hasMessages" class="title-section">
      <h1 class="title">董会答：懂你、懂美团的问答助手</h1>
      <div class="underline"></div>
    </div>

    <!-- 输入框区域 -->
    <div class="input-container">
      <input
        v-model="inputText"
        type="text"
        class="input-field"
        placeholder="请输入您的问题"
        @keyup.enter="handleSubmit"
      />
      <div class="button-group">
        <button class="submit-btn" @click="handleSubmit">
          提交
        </button>
        <button class="batch-test-btn" @click="handleBatchTest">
          批量测评
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, defineEmits, defineProps } from 'vue';
import { showFailToast } from 'vant';

interface IProps {
  hasMessages?: boolean;
}

const props = defineProps<IProps>();
const emit = defineEmits(['send-message', 'batch-test']);

const inputText = ref('');
const isLoading = ref(false);

const handleSubmit = () => {
  if (!inputText.value.trim()) {
    showFailToast('请输入问题内容');
    return;
  }

  if (isLoading.value) {
    showFailToast('请等待当前回复完成');
    return;
  }

  console.log('🚀 [InputSection] 提交问题:', inputText.value);
  const message = inputText.value.trim();
  inputText.value = ''; // 清空输入框
  emit('send-message', message);
};

const handleBatchTest = () => {
  if (!inputText.value.trim()) {
    showFailToast('请输入问题内容');
    return;
  }

  if (isLoading.value) {
    showFailToast('请等待当前回复完成');
    return;
  }

  console.log('🔄 [InputSection] 批量测评:', inputText.value);
  const message = inputText.value.trim();
  inputText.value = ''; // 清空输入框
  emit('batch-test', message);
};

// 暴露方法给父组件
defineExpose({
  setLoading: (loading: boolean) => {
    isLoading.value = loading;
  },
  clearInput: () => {
    inputText.value = '';
  },
});
</script>

<style lang="scss" scoped>
.input-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1300px;
  margin: 0 auto;
}

.title-section {
  display: flex;
  flex-direction: column;
  align-items: center;

  .title {
  font-size: 40px;
  font-weight: bold;
  color: #333;
  margin: 0 0 16px 0;
  text-align: center;
  }

  .underline {
    width: 60px;
    height: 4px;
    background: linear-gradient(90deg, #8B7ED8 0%, #B794F6 100%);
    border-radius: 2px;
    margin-bottom: 40px;
  }
}

.input-container {
  width: 100%;
  max-width: 1300px;
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 16px 28px;
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 50px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;

  &:focus-within {
    border-color: #8B7ED8;
    box-shadow: 0 4px 12px rgba(139, 126, 216, 0.2);
  }
}

.input-field {
  flex: 1;
  border: none;
  outline: none;
  background: transparent;
  font-size: 28px;
  color: #333;
  padding: 8px 0;

  &::placeholder {
    color: #999;
  }
}

.button-group {
  display: flex;
  gap: 12px;
}

.submit-btn,
.batch-test-btn {
  padding: 10px 24px;
  border: none;
  border-radius: 25px;
  font-size: 24px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.submit-btn {
  background: linear-gradient(90deg, #8B7ED8 0%, #B794F6 100%);
  color: white;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(139, 126, 216, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}

.batch-test-btn {
  background: linear-gradient(90deg, #A78BFA 0%, #C084FC 100%);
  color: white;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(167, 139, 250, 0.3);
  }

  &:active {
    transform: translateY(0);
  }
}
</style>
