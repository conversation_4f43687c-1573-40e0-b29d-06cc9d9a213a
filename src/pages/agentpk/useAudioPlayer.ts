import { ref, onUnmounted } from 'vue';
import type { Ref } from 'vue';
import { getStreamSynthesis, getTtsResponse } from './apis/tts';

/**
 * 音频队列状态接口
 */
export type IAudioType = 'manualPlay' | 'autoPlay';
export type AudioStatus = 'idle' | 'loading' | 'playing' | 'completed';

interface IAudioQueueState {
  isPlaying: Ref<boolean>;
  currentPlayingData: Ref<ICurrentPlayingData>;
  audioStatus: Ref<AudioStatus>;
  play: (audioQuery: ICurrentPlayingData) => void;
  stop: () => void;
  setVolume: (value: number) => void;
  isCurrentAudioPlaying: (id: string) => boolean;
}
interface ICurrentPlayingData {
  id: string;
  text: string;
  type: IAudioType;
}
// 创建一个全局单例
let instance: IAudioQueueState | null = null;

// 全局状态管理（移到函数外部）
const isPlaying = ref(false);
const currentPlayingData = ref<ICurrentPlayingData>({} as ICurrentPlayingData);
const volume = ref(1);
const audioStatus = ref<AudioStatus>('idle');

// 全局音频相关变量（移到函数外部）
let audioElement: HTMLAudioElement | null = null;
let mediaSource: MediaSource | null = null;
let sourceBuffer: SourceBuffer | null = null;
let abortController: AbortController | null = null;
let isDestroyed = false;

// 组件引用计数，用于管理全局资源的生命周期
let componentRefCount = 0;

/**
 * 音频队列管理 Hook
 */
export function useAudioQueue(): IAudioQueueState {
  // 增加引用计数
  componentRefCount++;

  // 如果之前被销毁了，现在有新组件使用，需要重置状态
  if (isDestroyed && componentRefCount > 0) {
    console.log('🔄 [useAudioPlayer] 重置已销毁的音频播放器状态');
    isDestroyed = false;
  }

  if (instance) {
    // 如果实例已存在，注册当前组件的卸载钩子
    onUnmounted(() => {
      componentRefCount--;
      // 只有当所有组件都卸载时才真正销毁资源
      if (componentRefCount <= 0) {
        console.log('🔄 [useAudioPlayer] 所有组件已卸载，清理全局音频资源');
        isDestroyed = true;
        // 调用实例的stop方法而不是未定义的stop函数
        if (instance && instance.stop) {
          instance.stop();
        }
        // 重置全局状态
        componentRefCount = 0;
        instance = null;
      }
    });
    return instance;
  }

  /**
   * 检查指定ID的音频是否正在播放
   */
  const isCurrentAudioPlaying = (id: string): boolean => {
    return isPlaying.value && currentPlayingData.value.id === id;
  };

  /**
   * 初始化音频上下文
   */
  const initAudioContext = (): void => {
    if (audioElement) {
      return;
    }

    audioElement = new Audio();
    audioElement.volume = volume.value;
    audioElement.preload = 'none';

    // 音频播放事件监听
    audioElement.addEventListener('play', () => {
      console.log('🎵 [useAudioPlayer] 音频开始播放');
      audioStatus.value = 'playing';
    });

    audioElement.addEventListener('ended', () => {
      console.log('🎵 [useAudioPlayer] 音频播放结束');
      cleanupCurrentAudio();
    });

    audioElement.addEventListener('error', (e) => {
      console.error('❌ [useAudioPlayer] 音频播放错误:', e);
      cleanupCurrentAudio();
    });

    audioElement.addEventListener('pause', () => {
      console.log('⏸️ [useAudioPlayer] 音频暂停');
      audioStatus.value = 'idle';
    });
  };

  /**
   * 清理当前音频播放状态
   */
  const cleanupCurrentAudio = () => {
    isPlaying.value = false;
    audioStatus.value = 'completed';
    currentPlayingData.value = {} as ICurrentPlayingData;

    if (abortController) {
      abortController.abort();
      abortController = null;
    }



    // 清理 MediaSource 相关资源
    if (sourceBuffer) {
      try {
        if (mediaSource && mediaSource.readyState === 'open') {
          mediaSource.removeSourceBuffer(sourceBuffer);
        }
      } catch (error) {
        console.warn('⚠️ [useAudioPlayer] 清理 SourceBuffer 时出错:', error);
      }
      sourceBuffer = null;
    }

    if (mediaSource) {
      try {
        if (mediaSource.readyState === 'open') {
          mediaSource.endOfStream();
        }
      } catch (error) {
        console.warn('⚠️ [useAudioPlayer] 结束 MediaSource 时出错:', error);
      }
      mediaSource = null;
    }

    if (audioElement) {
      audioElement.pause();
      audioElement.src = '';
      audioElement.load();
    }
  };

  /**
   * 停止当前播放
   */
  const stop = () => {
    console.log('⏹️ [useAudioPlayer] 停止播放');
    cleanupCurrentAudio();
  };

  /**
   * 设置音量
   */
  const setVolume = (value: number) => {
    volume.value = Math.max(0, Math.min(1, value));
    if (audioElement) {
      audioElement.volume = volume.value;
    }
  };

  /**
   * 播放指定ID的音频
   */
  const play = async (audioQuery: ICurrentPlayingData) => {
    console.log(audioQuery);
    console.log((!audioQuery.id && !audioQuery.text) || isDestroyed);
    if ((!audioQuery.id && !audioQuery.text) || isDestroyed) {
      console.log(isDestroyed);
      return;
    }
    console.log(99988);
    // 如果当前有音频在播放，先停止并清理
    if (isPlaying.value) {
      stop();
    }

    currentPlayingData.value = audioQuery;
    isPlaying.value = true;
    audioStatus.value = 'loading';

    try {
      initAudioContext();
      await streamAudio(audioQuery);
    } catch (error) {
      cleanupCurrentAudio();
      throw new Error(`播放失败: ${error}`);
    }
  };

  /**
   * 流式获取并播放音频
   */
  const streamAudio = async (audioQuery: {
    id: string;
    text: string;
    type: string;
  }): Promise<void> => {
    if (!audioElement) {
      throw new Error(`音频上下文未正确初始化`);
    }

    abortController = new AbortController();

    try {
      let response;
      if (audioQuery.type === 'autoPlay') {
        response = await getTtsResponse({
          id: audioQuery.id,
          signal: abortController.signal,
        });
      } else {
        response = await getStreamSynthesis({
          text: audioQuery.text,
          signal: abortController.signal,
        });
      }
      if (!response.ok) {
        audioStatus.value = 'idle';
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const contentType = response.headers.get('Content-Type') || '';
      if (!contentType.includes('audio/')) {
        throw new Error(`不支持的内容类型: ${contentType}`);
      }

      audioElement.preload = 'auto';
      const blob = await response.blob();
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      const audioUrl = URL.createObjectURL(blob);
      audioElement.src = audioUrl;

      // 播放音频
      await audioElement.play();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        console.log('🔄 [useAudioPlayer] 音频请求被取消');
        return;
      }
      throw error;
    }
  };

  /**
   * 组件卸载时清理
   */
  onUnmounted(() => {
    componentRefCount--;
    // 只有当所有组件都卸载时才真正销毁资源
    if (componentRefCount <= 0) {
      console.log('🔄 [useAudioPlayer] 所有组件已卸载，清理全局音频资源');
      isDestroyed = true;
      // 直接调用stop函数，此时已经定义了
      stop();
      // 重置全局状态
      componentRefCount = 0;
      instance = null;
    }
  });

  instance = {
    isPlaying,
    currentPlayingData,
    audioStatus,
    play,
    stop,
    setVolume,
    isCurrentAudioPlaying,
  };

  return instance;
}
